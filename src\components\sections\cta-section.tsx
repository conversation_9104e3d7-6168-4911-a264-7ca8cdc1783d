'use client';

import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, FileText, MessageCircle } from 'lucide-react';

export function CTASection() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <section className="py-20 bg-gradient-to-r from-mercy-600 via-mercy-700 to-mercy-800 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 relative">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="text-lg md:text-xl text-mercy-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join thousands of entrepreneurs who have already started their journey to financial independence. 
            Apply for microfinance today and take the first step towards building a sustainable future.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Button 
              size="xl" 
              variant="secondary"
              className="bg-white text-mercy-700 hover:bg-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 group"
              asChild
            >
              <Link href={`/${locale}/apply`}>
                <FileText className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2 group-hover:scale-110 transition-transform" />
                Start Your Application
                <ArrowRight className="h-5 w-5 ml-2 rtl:ml-0 rtl:mr-2 group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform" />
              </Link>
            </Button>
            
            <Button 
              size="xl" 
              variant="outline"
              className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-300 group"
              asChild
            >
              <Link href={`/${locale}/contact`}>
                <MessageCircle className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2 group-hover:scale-110 transition-transform" />
                Talk to an Expert
              </Link>
            </Button>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-white">
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">1</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Quick Application</h3>
              <p className="text-mercy-100 text-sm">
                Complete your application in just 15 minutes with our streamlined process
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">2</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Fast Approval</h3>
              <p className="text-mercy-100 text-sm">
                Get a decision within 48-72 hours from our expert review team
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">3</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Ongoing Support</h3>
              <p className="text-mercy-100 text-sm">
                Receive continuous mentorship and business development support
              </p>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-16 pt-8 border-t border-white/20">
            <p className="text-mercy-100 text-sm mb-4">Trusted by entrepreneurs worldwide</p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              <div className="text-white font-semibold">15,000+ Businesses</div>
              <div className="w-px h-6 bg-white/30"></div>
              <div className="text-white font-semibold">98% Success Rate</div>
              <div className="w-px h-6 bg-white/30"></div>
              <div className="text-white font-semibold">$25M+ Disbursed</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
