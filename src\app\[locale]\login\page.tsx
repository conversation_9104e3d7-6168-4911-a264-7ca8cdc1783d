import { useTranslations } from 'next-intl';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { LoginForm } from '@/components/forms/login-form';
import Link from 'next/link';
import { HandHeart } from 'lucide-react';

export default function LoginPage() {
  const t = useTranslations();

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 flex items-center justify-center py-12 bg-gray-50 dark:bg-gray-900">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <Link href="/en" className="inline-flex items-center space-x-2 rtl:space-x-reverse mb-6">
              <div className="flex items-center justify-center w-12 h-12 bg-mercy-600 rounded-lg">
                <HandHeart className="h-7 w-7 text-white" />
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-mercy-700 dark:text-mercy-400">
                  Mercy Corps
                </span>
                <span className="text-sm text-muted-foreground">
                  Microfinance
                </span>
              </div>
            </Link>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {t('auth.login.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Access your microfinance dashboard and manage your applications
            </p>
          </div>
          <LoginForm />
        </div>
      </main>
      <Footer />
    </div>
  );
}
