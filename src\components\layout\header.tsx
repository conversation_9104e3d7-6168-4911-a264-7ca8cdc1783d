'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { 
  Menu, 
  X, 
  Sun, 
  Moon, 
  Globe, 
  User,
  ChevronDown,
  Heart,
  HandHeart
} from 'lucide-react';
import { cn } from '@/lib/utils';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLangMenuOpen, setIsLangMenuOpen] = useState(false);
  const t = useTranslations();
  const locale = useLocale();
  const { theme, setTheme } = useTheme();

  const navigation = [
    { name: t('navigation.home'), href: `/${locale}` },
    { name: t('navigation.about'), href: `/${locale}/about` },
    { name: t('navigation.services'), href: `/${locale}/services` },
    { name: t('navigation.apply'), href: `/${locale}/apply` },
    { name: t('navigation.contact'), href: `/${locale}/contact` },
  ];

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleLangMenu = () => setIsLangMenuOpen(!isLangMenuOpen);

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="flex items-center justify-center w-10 h-10 bg-mercy-600 rounded-lg">
              <HandHeart className="h-6 w-6 text-white" />
            </div>
            <div className="flex flex-col">
              <span className="text-lg font-bold text-mercy-700 dark:text-mercy-400">
                Mercy Corps
              </span>
              <span className="text-xs text-muted-foreground">
                Microfinance
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6 rtl:space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4 rtl:space-x-reverse">
            {/* Language Switcher */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleLangMenu}
                className="flex items-center space-x-1 rtl:space-x-reverse"
              >
                <Globe className="h-4 w-4" />
                <span className="text-sm">{locale.toUpperCase()}</span>
                <ChevronDown className="h-3 w-3" />
              </Button>
              {isLangMenuOpen && (
                <div className="absolute top-full mt-1 right-0 rtl:right-auto rtl:left-0 bg-background border rounded-md shadow-lg py-1 min-w-[100px]">
                  <Link
                    href="/en"
                    className="block px-3 py-2 text-sm hover:bg-accent"
                    onClick={() => setIsLangMenuOpen(false)}
                  >
                    English
                  </Link>
                  <Link
                    href="/ar"
                    className="block px-3 py-2 text-sm hover:bg-accent"
                    onClick={() => setIsLangMenuOpen(false)}
                  >
                    العربية
                  </Link>
                </div>
              )}
            </div>

            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>

            {/* Auth Buttons */}
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/${locale}/login`}>
                <User className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
                {t('navigation.login')}
              </Link>
            </Button>
            <Button variant="mercy" size="sm" asChild>
              <Link href={`/${locale}/register`}>
                {t('navigation.register')}
              </Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={toggleMenu}
          >
            {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t bg-background">
            <nav className="flex flex-col space-y-1 p-4">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-sm font-medium text-muted-foreground hover:text-foreground py-2 px-3 rounded-md hover:bg-accent transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              
              <div className="border-t pt-4 mt-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Globe className="h-4 w-4" />
                    <span className="text-sm font-medium">Language</span>
                  </div>
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <Link
                      href="/en"
                      className={cn(
                        "px-2 py-1 text-xs rounded",
                        locale === 'en' ? 'bg-mercy-100 text-mercy-700' : 'text-muted-foreground'
                      )}
                    >
                      EN
                    </Link>
                    <Link
                      href="/ar"
                      className={cn(
                        "px-2 py-1 text-xs rounded",
                        locale === 'ar' ? 'bg-mercy-100 text-mercy-700' : 'text-muted-foreground'
                      )}
                    >
                      AR
                    </Link>
                  </div>
                </div>
                
                <div className="flex items-center justify-between mb-4">
                  <span className="text-sm font-medium">Theme</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                  >
                    <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                    <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                  </Button>
                </div>

                <div className="space-y-2">
                  <Button variant="outline" className="w-full" asChild>
                    <Link href={`/${locale}/login`}>
                      <User className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
                      {t('navigation.login')}
                    </Link>
                  </Button>
                  <Button variant="mercy" className="w-full" asChild>
                    <Link href={`/${locale}/register`}>
                      {t('navigation.register')}
                    </Link>
                  </Button>
                </div>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
