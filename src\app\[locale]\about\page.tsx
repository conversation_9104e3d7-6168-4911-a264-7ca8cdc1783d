import { useTranslations } from 'next-intl';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Heart, 
  Users, 
  Globe, 
  Target, 
  Award, 
  TrendingUp,
  Shield,
  Handshake
} from 'lucide-react';

const values = [
  {
    icon: Heart,
    title: 'Compassion',
    description: 'We believe in the power of human kindness and work with empathy to understand and address the needs of communities.',
    color: 'text-red-600 dark:text-red-400'
  },
  {
    icon: Users,
    title: 'Collaboration',
    description: 'We work together with local communities, partners, and stakeholders to create sustainable solutions.',
    color: 'text-blue-600 dark:text-blue-400'
  },
  {
    icon: Globe,
    title: 'Global Impact',
    description: 'Our reach spans across continents, bringing financial inclusion to underserved communities worldwide.',
    color: 'text-green-600 dark:text-green-400'
  },
  {
    icon: Target,
    title: 'Results-Driven',
    description: 'We measure our success by the positive impact we create in the lives of the people we serve.',
    color: 'text-purple-600 dark:text-purple-400'
  }
];

const achievements = [
  {
    icon: Award,
    title: 'Excellence in Microfinance',
    description: 'Recognized globally for innovative microfinance solutions',
    year: '2023'
  },
  {
    icon: TrendingUp,
    title: 'Sustainable Growth',
    description: '98% loan repayment rate across all programs',
    year: '2023'
  },
  {
    icon: Shield,
    title: 'Trust & Security',
    description: 'Bank-level security for all financial transactions',
    year: 'Ongoing'
  },
  {
    icon: Handshake,
    title: 'Community Partnership',
    description: 'Strong partnerships with 500+ local organizations',
    year: '2023'
  }
];

export default function AboutPage() {
  const t = useTranslations();

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-mercy-600 via-mercy-700 to-mercy-800 text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                {t('navigation.about')}
              </h1>
              <p className="text-xl md:text-2xl text-mercy-100 leading-relaxed">
                Mercy Corps is a leading global organization powered by the belief that a better world is possible. 
                We work in the world's toughest places, helping people survive and thrive in the face of crisis and hardship.
              </p>
            </div>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                  Our Mission
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8">
                  To alleviate suffering, poverty, and oppression by helping people build secure, 
                  productive, and just communities. Through our microfinance programs, we provide 
                  the tools and resources needed for sustainable economic growth.
                </p>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 rtl:space-x-reverse">
                    <div className="flex-shrink-0 w-6 h-6 bg-mercy-100 dark:bg-mercy-900 rounded-full flex items-center justify-center mt-1">
                      <div className="w-2 h-2 bg-mercy-600 dark:bg-mercy-400 rounded-full"></div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">
                      Provide accessible financial services to underserved communities
                    </p>
                  </div>
                  <div className="flex items-start space-x-3 rtl:space-x-reverse">
                    <div className="flex-shrink-0 w-6 h-6 bg-mercy-100 dark:bg-mercy-900 rounded-full flex items-center justify-center mt-1">
                      <div className="w-2 h-2 bg-mercy-600 dark:bg-mercy-400 rounded-full"></div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">
                      Empower entrepreneurs with training and mentorship
                    </p>
                  </div>
                  <div className="flex items-start space-x-3 rtl:space-x-reverse">
                    <div className="flex-shrink-0 w-6 h-6 bg-mercy-100 dark:bg-mercy-900 rounded-full flex items-center justify-center mt-1">
                      <div className="w-2 h-2 bg-mercy-600 dark:bg-mercy-400 rounded-full"></div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">
                      Build resilient economic systems that withstand crises
                    </p>
                  </div>
                </div>
              </div>
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                  alt="Our Mission"
                  className="rounded-2xl shadow-2xl"
                />
                <div className="absolute -bottom-6 -right-6 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-xl border">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-mercy-600 dark:text-mercy-400">25+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Years of Impact</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Values */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Our Values
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                These core values guide everything we do and shape how we work with communities around the world.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => {
                const Icon = value.icon;
                return (
                  <Card key={index} className="text-center hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                    <CardHeader>
                      <div className="mx-auto mb-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-full w-fit">
                        <Icon className={`h-8 w-8 ${value.color}`} />
                      </div>
                      <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">
                        {value.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        {value.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Achievements */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Our Achievements
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Recognition and milestones that reflect our commitment to excellence in microfinance.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {achievements.map((achievement, index) => {
                const Icon = achievement.icon;
                return (
                  <Card key={index} className="hover:shadow-lg transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4 rtl:space-x-reverse">
                        <div className="flex-shrink-0 p-3 bg-mercy-100 dark:bg-mercy-900 rounded-lg">
                          <Icon className="h-6 w-6 text-mercy-600 dark:text-mercy-400" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              {achievement.title}
                            </h3>
                            <span className="text-sm text-mercy-600 dark:text-mercy-400 font-medium">
                              {achievement.year}
                            </span>
                          </div>
                          <p className="text-gray-600 dark:text-gray-300">
                            {achievement.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Global Presence
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Our microfinance programs operate in over 40 countries, reaching communities in need of financial services.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="bg-white dark:bg-gray-700 p-8 rounded-2xl shadow-lg">
                <div className="text-4xl font-bold text-mercy-600 dark:text-mercy-400 mb-2">40+</div>
                <div className="text-gray-600 dark:text-gray-300 font-medium">Countries</div>
                <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  Active microfinance programs
                </div>
              </div>
              <div className="bg-white dark:bg-gray-700 p-8 rounded-2xl shadow-lg">
                <div className="text-4xl font-bold text-mercy-600 dark:text-mercy-400 mb-2">500+</div>
                <div className="text-gray-600 dark:text-gray-300 font-medium">Local Partners</div>
                <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  Community organizations
                </div>
              </div>
              <div className="bg-white dark:bg-gray-700 p-8 rounded-2xl shadow-lg">
                <div className="text-4xl font-bold text-mercy-600 dark:text-mercy-400 mb-2">1M+</div>
                <div className="text-gray-600 dark:text-gray-300 font-medium">Lives Impacted</div>
                <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  Through our programs
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
