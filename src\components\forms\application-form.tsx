'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  User, 
  Building, 
  DollarSign, 
  FileText, 
  CheckCircle,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

const applicationSchema = z.object({
  // Personal Information
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
  address: z.string().min(10, 'Please enter your full address'),
  
  // Business Information
  businessName: z.string().min(2, 'Business name must be at least 2 characters'),
  businessType: z.string().min(1, 'Please select a business type'),
  businessDescription: z.string().min(50, 'Please provide a detailed business description'),
  
  // Financial Information
  loanAmount: z.number().min(100, 'Minimum loan amount is $100').max(50000, 'Maximum loan amount is $50,000'),
  loanPurpose: z.string().min(20, 'Please explain the purpose of the loan'),
  monthlyIncome: z.number().min(0, 'Please enter your monthly income'),
  monthlyExpenses: z.number().min(0, 'Please enter your monthly expenses'),
  
  // Additional Information
  hasCollateral: z.boolean(),
  collateralDescription: z.string().optional(),
  previousLoan: z.boolean(),
  businessExperience: z.number().min(0, 'Please enter years of business experience'),
});

type ApplicationFormData = z.infer<typeof applicationSchema>;

const steps = [
  {
    id: 'personal',
    title: 'Personal Information',
    icon: User,
    fields: ['firstName', 'lastName', 'email', 'phone', 'address']
  },
  {
    id: 'business',
    title: 'Business Information',
    icon: Building,
    fields: ['businessName', 'businessType', 'businessDescription', 'businessExperience']
  },
  {
    id: 'financial',
    title: 'Financial Information',
    icon: DollarSign,
    fields: ['loanAmount', 'loanPurpose', 'monthlyIncome', 'monthlyExpenses']
  },
  {
    id: 'additional',
    title: 'Additional Information',
    icon: FileText,
    fields: ['hasCollateral', 'collateralDescription', 'previousLoan']
  }
];

const businessTypes = [
  'Agriculture',
  'Retail/Trading',
  'Manufacturing',
  'Services',
  'Technology',
  'Food & Beverage',
  'Handicrafts',
  'Transportation',
  'Other'
];

export function ApplicationForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const t = useTranslations();

  const {
    register,
    handleSubmit,
    formState: { errors },
    trigger,
    watch,
    setValue
  } = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationSchema),
    mode: 'onChange'
  });

  const watchedValues = watch();

  const validateCurrentStep = async () => {
    const currentStepFields = steps[currentStep].fields;
    const isValid = await trigger(currentStepFields as any);
    return isValid;
  };

  const nextStep = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = async (data: ApplicationFormData) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Application submitted:', data);
      toast.success('Application submitted successfully! We will review it within 48-72 hours.');
      
      // Reset form or redirect
    } catch (error) {
      toast.error('Failed to submit application. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('application.form.first_name')}
                </label>
                <Input
                  {...register('firstName')}
                  placeholder="Enter your first name"
                  className={cn(errors.firstName && "border-red-500")}
                />
                {errors.firstName && (
                  <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('application.form.last_name')}
                </label>
                <Input
                  {...register('lastName')}
                  placeholder="Enter your last name"
                  className={cn(errors.lastName && "border-red-500")}
                />
                {errors.lastName && (
                  <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('application.form.email')}
              </label>
              <Input
                {...register('email')}
                type="email"
                placeholder="Enter your email address"
                className={cn(errors.email && "border-red-500")}
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('application.form.phone')}
              </label>
              <Input
                {...register('phone')}
                type="tel"
                placeholder="Enter your phone number"
                className={cn(errors.phone && "border-red-500")}
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('application.form.address')}
              </label>
              <textarea
                {...register('address')}
                rows={3}
                placeholder="Enter your full address"
                className={cn(
                  "flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                  errors.address && "border-red-500"
                )}
              />
              {errors.address && (
                <p className="text-red-500 text-sm mt-1">{errors.address.message}</p>
              )}
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('application.form.business_name')}
              </label>
              <Input
                {...register('businessName')}
                placeholder="Enter your business name"
                className={cn(errors.businessName && "border-red-500")}
              />
              {errors.businessName && (
                <p className="text-red-500 text-sm mt-1">{errors.businessName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('application.form.business_type')}
              </label>
              <select
                {...register('businessType')}
                className={cn(
                  "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                  errors.businessType && "border-red-500"
                )}
              >
                <option value="">Select business type</option>
                {businessTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              {errors.businessType && (
                <p className="text-red-500 text-sm mt-1">{errors.businessType.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Business Description
              </label>
              <textarea
                {...register('businessDescription')}
                rows={4}
                placeholder="Describe your business, products/services, and target market"
                className={cn(
                  "flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                  errors.businessDescription && "border-red-500"
                )}
              />
              {errors.businessDescription && (
                <p className="text-red-500 text-sm mt-1">{errors.businessDescription.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Years of Business Experience
              </label>
              <Input
                {...register('businessExperience', { valueAsNumber: true })}
                type="number"
                min="0"
                placeholder="Enter years of experience"
                className={cn(errors.businessExperience && "border-red-500")}
              />
              {errors.businessExperience && (
                <p className="text-red-500 text-sm mt-1">{errors.businessExperience.message}</p>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('application.form.loan_amount')} (USD)
              </label>
              <Input
                {...register('loanAmount', { valueAsNumber: true })}
                type="number"
                min="100"
                max="50000"
                placeholder="Enter requested loan amount"
                className={cn(errors.loanAmount && "border-red-500")}
              />
              {errors.loanAmount && (
                <p className="text-red-500 text-sm mt-1">{errors.loanAmount.message}</p>
              )}
              <p className="text-sm text-gray-500 mt-1">Minimum: $100, Maximum: $50,000</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('application.form.loan_purpose')}
              </label>
              <textarea
                {...register('loanPurpose')}
                rows={3}
                placeholder="Explain how you will use the loan funds"
                className={cn(
                  "flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                  errors.loanPurpose && "border-red-500"
                )}
              />
              {errors.loanPurpose && (
                <p className="text-red-500 text-sm mt-1">{errors.loanPurpose.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('application.form.monthly_income')} (USD)
                </label>
                <Input
                  {...register('monthlyIncome', { valueAsNumber: true })}
                  type="number"
                  min="0"
                  placeholder="Enter monthly income"
                  className={cn(errors.monthlyIncome && "border-red-500")}
                />
                {errors.monthlyIncome && (
                  <p className="text-red-500 text-sm mt-1">{errors.monthlyIncome.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('application.form.monthly_expenses')} (USD)
                </label>
                <Input
                  {...register('monthlyExpenses', { valueAsNumber: true })}
                  type="number"
                  min="0"
                  placeholder="Enter monthly expenses"
                  className={cn(errors.monthlyExpenses && "border-red-500")}
                />
                {errors.monthlyExpenses && (
                  <p className="text-red-500 text-sm mt-1">{errors.monthlyExpenses.message}</p>
                )}
              </div>
            </div>

            {watchedValues.monthlyIncome && watchedValues.monthlyExpenses && (
              <div className="bg-mercy-50 dark:bg-mercy-900/20 p-4 rounded-lg">
                <p className="text-sm text-mercy-700 dark:text-mercy-300">
                  <strong>Net Monthly Income:</strong> ${(watchedValues.monthlyIncome - watchedValues.monthlyExpenses).toLocaleString()}
                </p>
              </div>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                Do you have collateral to offer?
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    {...register('hasCollateral')}
                    type="radio"
                    value="true"
                    className="mr-2"
                    onChange={() => setValue('hasCollateral', true)}
                  />
                  Yes
                </label>
                <label className="flex items-center">
                  <input
                    {...register('hasCollateral')}
                    type="radio"
                    value="false"
                    className="mr-2"
                    onChange={() => setValue('hasCollateral', false)}
                  />
                  No
                </label>
              </div>
            </div>

            {watchedValues.hasCollateral && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Collateral Description
                </label>
                <textarea
                  {...register('collateralDescription')}
                  rows={3}
                  placeholder="Describe the collateral you can offer"
                  className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                Have you taken a business loan before?
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    {...register('previousLoan')}
                    type="radio"
                    value="true"
                    className="mr-2"
                    onChange={() => setValue('previousLoan', true)}
                  />
                  Yes
                </label>
                <label className="flex items-center">
                  <input
                    {...register('previousLoan')}
                    type="radio"
                    value="false"
                    className="mr-2"
                    onChange={() => setValue('previousLoan', false)}
                  />
                  No
                </label>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
              <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                Required Documents
              </h3>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• Valid government-issued ID</li>
                <li>• Business registration documents</li>
                <li>• Bank statements (last 3 months)</li>
                <li>• Business plan or proposal</li>
                <li>• Proof of address</li>
              </ul>
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-3">
                You will be contacted to submit these documents after your initial application is reviewed.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="shadow-xl border-0">
      <CardHeader className="text-center pb-8">
        <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
          Microfinance Application
        </CardTitle>
        <CardDescription className="text-gray-600 dark:text-gray-300">
          Complete all steps to submit your application
        </CardDescription>
      </CardHeader>

      <CardContent>
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={cn(
                    "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300",
                    isActive ? "border-mercy-600 bg-mercy-600 text-white" :
                    isCompleted ? "border-green-500 bg-green-500 text-white" :
                    "border-gray-300 bg-white text-gray-400"
                  )}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={cn(
                      "w-16 h-0.5 mx-2 transition-all duration-300",
                      isCompleted ? "bg-green-500" : "bg-gray-300"
                    )} />
                  )}
                </div>
              );
            })}
          </div>
          
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {steps[currentStep].title}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Step {currentStep + 1} of {steps.length}
            </p>
          </div>
        </div>

        {/* Form Content */}
        <form onSubmit={handleSubmit(onSubmit)}>
          {renderStepContent()}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button
                type="submit"
                variant="mercy"
                disabled={isSubmitting}
                className="flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <div className="loading-dots mr-2">
                      <div></div>
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                    Submitting...
                  </>
                ) : (
                  <>
                    Submit Application
                    <CheckCircle className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>
            ) : (
              <Button
                type="button"
                variant="mercy"
                onClick={nextStep}
                className="flex items-center"
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
