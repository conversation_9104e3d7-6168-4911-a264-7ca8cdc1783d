'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react';
import { cn } from '@/lib/utils';

const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON>-Zahra',
    role: 'Small Business Owner',
    location: 'Jordan',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    content: 'Thanks to Mercy Corps microfinance, I was able to expand my tailoring business and hire three additional women from my community. The financial training they provided was invaluable.',
    rating: 5,
    impact: 'Created 3 jobs'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Agricultural Entrepreneur',
    location: 'Egypt',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    content: 'The loan helped me purchase modern farming equipment. My crop yield increased by 40%, and I can now support my family while contributing to food security in our region.',
    rating: 5,
    impact: '40% yield increase'
  },
  {
    id: 3,
    name: 'Maria Santos',
    role: 'Restaurant Owner',
    location: 'Philippines',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    content: 'Starting my restaurant seemed impossible until I found Mercy Corps. Their support went beyond just funding - they provided mentorship that helped me build a sustainable business.',
    rating: 5,
    impact: 'Sustainable business'
  },
  {
    id: 4,
    name: 'David Ochieng',
    role: 'Tech Entrepreneur',
    location: 'Kenya',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    content: 'With their microfinance support, I launched a mobile app that connects farmers with buyers. We\'ve helped over 500 farmers increase their income by 30%.',
    rating: 5,
    impact: '500+ farmers helped'
  },
  {
    id: 5,
    name: 'Priya Sharma',
    role: 'Handicraft Artisan',
    location: 'India',
    image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    content: 'The microfinance program helped me scale my handicraft business online. I now export to 15 countries and employ 20 women artisans from my village.',
    rating: 5,
    impact: '20 women employed'
  }
];

export function TestimonialsSection() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const t = useTranslations();

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const goToTestimonial = (index: number) => {
    setCurrentTestimonial(index);
  };

  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            {t('home.testimonials.title')}
          </h2>
          <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {t('home.testimonials.subtitle')}
          </p>
        </div>

        {/* Main Testimonial Display */}
        <div className="relative max-w-4xl mx-auto mb-12">
          <Card className="border-0 shadow-2xl bg-gradient-to-br from-mercy-50 to-white dark:from-gray-800 dark:to-gray-700">
            <CardContent className="p-8 md:p-12">
              <div className="flex flex-col md:flex-row items-center gap-8">
                {/* Profile Image */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    <img
                      src={testimonials[currentTestimonial].image}
                      alt={testimonials[currentTestimonial].name}
                      className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover border-4 border-mercy-200 dark:border-mercy-600"
                    />
                    <div className="absolute -top-2 -right-2 bg-mercy-600 text-white p-2 rounded-full">
                      <Quote className="h-4 w-4" />
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 text-center md:text-left">
                  <div className="flex justify-center md:justify-start mb-4">
                    {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  
                  <blockquote className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-6 leading-relaxed italic">
                    "{testimonials[currentTestimonial].content}"
                  </blockquote>
                  
                  <div className="space-y-2">
                    <div className="font-semibold text-gray-900 dark:text-white text-lg">
                      {testimonials[currentTestimonial].name}
                    </div>
                    <div className="text-mercy-600 dark:text-mercy-400 font-medium">
                      {testimonials[currentTestimonial].role}
                    </div>
                    <div className="text-gray-500 dark:text-gray-400">
                      {testimonials[currentTestimonial].location}
                    </div>
                    <div className="inline-block bg-mercy-100 dark:bg-mercy-900 text-mercy-700 dark:text-mercy-300 px-3 py-1 rounded-full text-sm font-medium">
                      Impact: {testimonials[currentTestimonial].impact}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Navigation Arrows */}
          <Button
            variant="outline"
            size="icon"
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Testimonial Indicators */}
        <div className="flex justify-center space-x-2 mb-12">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToTestimonial(index)}
              className={cn(
                "w-3 h-3 rounded-full transition-all duration-300",
                index === currentTestimonial
                  ? "bg-mercy-600 scale-125"
                  : "bg-gray-300 dark:bg-gray-600 hover:bg-mercy-400"
              )}
            />
          ))}
        </div>

        {/* Testimonial Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {testimonials.slice(0, 3).map((testimonial, index) => (
            <Card 
              key={testimonial.id}
              className="hover:shadow-lg transition-all duration-300 cursor-pointer"
              onClick={() => goToTestimonial(index)}
            >
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 rtl:space-x-reverse mb-4">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {testimonial.location}
                    </div>
                  </div>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-3">
                  "{testimonial.content}"
                </p>
                <div className="flex justify-between items-center mt-4">
                  <div className="flex">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <span className="text-xs text-mercy-600 dark:text-mercy-400 font-medium">
                    {testimonial.impact}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
