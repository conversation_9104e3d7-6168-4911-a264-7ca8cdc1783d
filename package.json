{"name": "mercy-microfinance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "eslint": "^8", "eslint-config-next": "14.0.4", "@supabase/supabase-js": "^2.38.4", "@supabase/auth-helpers-nextjs": "^0.8.7", "next-intl": "^3.4.0", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2", "lucide-react": "^0.294.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "framer-motion": "^10.16.16", "swiper": "^11.0.5", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "recharts": "^2.8.0", "@radix-ui/react-slot": "^1.0.2", "next-themes": "^0.2.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}}