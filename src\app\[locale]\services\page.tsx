import { useTranslations } from 'next-intl';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { 
  DollarSign, 
  GraduationCap, 
  Users, 
  TrendingUp, 
  Shield, 
  Clock,
  CheckCircle,
  ArrowRight,
  Calculator,
  FileText,
  Handshake,
  BarChart3
} from 'lucide-react';

const services = [
  {
    icon: DollarSign,
    title: 'Microloans',
    description: 'Small business loans ranging from $100 to $50,000 with flexible repayment terms',
    features: [
      'Competitive interest rates',
      'Flexible repayment schedules',
      'No hidden fees',
      'Quick approval process'
    ],
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-50 dark:bg-green-900/20'
  },
  {
    icon: GraduationCap,
    title: 'Financial Training',
    description: 'Comprehensive financial literacy and business management training programs',
    features: [
      'Business planning workshops',
      'Financial management courses',
      'Digital literacy training',
      'Marketing and sales guidance'
    ],
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20'
  },
  {
    icon: Users,
    title: 'Group Lending',
    description: 'Community-based lending circles that provide mutual support and accountability',
    features: [
      'Peer support networks',
      'Shared responsibility',
      'Lower interest rates',
      'Community building'
    ],
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20'
  },
  {
    icon: TrendingUp,
    title: 'Business Development',
    description: 'Ongoing mentorship and support to help your business grow and succeed',
    features: [
      'One-on-one mentoring',
      'Market analysis',
      'Growth strategy planning',
      'Networking opportunities'
    ],
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20'
  },
  {
    icon: Shield,
    title: 'Insurance Products',
    description: 'Microinsurance products to protect your business and family',
    features: [
      'Business insurance',
      'Health insurance',
      'Crop insurance',
      'Life insurance'
    ],
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-50 dark:bg-red-900/20'
  },
  {
    icon: Calculator,
    title: 'Savings Programs',
    description: 'Structured savings programs to help you build financial reserves',
    features: [
      'Goal-based savings',
      'Emergency funds',
      'Investment guidance',
      'Financial planning'
    ],
    color: 'text-indigo-600 dark:text-indigo-400',
    bgColor: 'bg-indigo-50 dark:bg-indigo-900/20'
  }
];

const loanTypes = [
  {
    title: 'Startup Loans',
    amount: '$100 - $5,000',
    term: '6-24 months',
    rate: '8-12% APR',
    description: 'Perfect for new entrepreneurs starting their first business'
  },
  {
    title: 'Growth Loans',
    amount: '$1,000 - $25,000',
    term: '12-36 months',
    rate: '6-10% APR',
    description: 'Expand your existing business with additional capital'
  },
  {
    title: 'Equipment Loans',
    amount: '$500 - $50,000',
    term: '12-60 months',
    rate: '5-9% APR',
    description: 'Purchase equipment and machinery for your business'
  },
  {
    title: 'Working Capital',
    amount: '$200 - $15,000',
    term: '3-18 months',
    rate: '7-11% APR',
    description: 'Manage cash flow and operational expenses'
  }
];

export default function ServicesPage() {
  const t = useTranslations();

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-mercy-600 via-mercy-700 to-mercy-800 text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                {t('navigation.services')}
              </h1>
              <p className="text-xl md:text-2xl text-mercy-100 leading-relaxed">
                Comprehensive financial solutions designed to empower entrepreneurs 
                and small businesses in underserved communities.
              </p>
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Our Financial Services
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                We offer a complete range of financial services tailored to meet the unique needs 
                of entrepreneurs and small businesses in developing communities.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => {
                const Icon = service.icon;
                return (
                  <Card key={index} className="hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-0 shadow-lg">
                    <CardHeader>
                      <div className={`mx-auto mb-4 p-4 ${service.bgColor} rounded-full w-fit`}>
                        <Icon className={`h-8 w-8 ${service.color}`} />
                      </div>
                      <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white text-center">
                        {service.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <CardDescription className="text-gray-600 dark:text-gray-300 leading-relaxed text-center">
                        {service.description}
                      </CardDescription>
                      <ul className="space-y-2">
                        {service.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                            <CheckCircle className={`h-4 w-4 ${service.color} flex-shrink-0`} />
                            <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Loan Types */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Loan Products
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Choose from our range of loan products designed to meet different business needs and stages.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {loanTypes.map((loan, index) => (
                <Card key={index} className="hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {loan.title}
                      </h3>
                      <div className="text-right">
                        <div className="text-lg font-bold text-mercy-600 dark:text-mercy-400">
                          {loan.amount}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {loan.term}
                        </div>
                      </div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {loan.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Interest Rate: <span className="text-mercy-600 dark:text-mercy-400">{loan.rate}</span>
                      </span>
                      <Button variant="outline" size="sm">
                        Learn More
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                How It Works
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Our streamlined process makes it easy to access the financial services you need.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="bg-mercy-100 dark:bg-mercy-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <FileText className="h-8 w-8 text-mercy-600 dark:text-mercy-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  1. Apply Online
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Complete our simple online application in just 15 minutes
                </p>
              </div>

              <div className="text-center">
                <div className="bg-mercy-100 dark:bg-mercy-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="h-8 w-8 text-mercy-600 dark:text-mercy-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  2. Review Process
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Our team reviews your application within 48-72 hours
                </p>
              </div>

              <div className="text-center">
                <div className="bg-mercy-100 dark:bg-mercy-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Handshake className="h-8 w-8 text-mercy-600 dark:text-mercy-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  3. Approval & Funding
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Once approved, funds are disbursed quickly to your account
                </p>
              </div>

              <div className="text-center">
                <div className="bg-mercy-100 dark:bg-mercy-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="h-8 w-8 text-mercy-600 dark:text-mercy-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  4. Ongoing Support
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Receive continuous support and mentorship for your business
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-mercy-600">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center text-white">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Get Started?
              </h2>
              <p className="text-xl text-mercy-100 mb-8 leading-relaxed">
                Take the first step towards financial independence and business growth. 
                Apply for our microfinance services today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" variant="secondary" asChild>
                  <Link href="/en/apply">
                    Start Application
                    <ArrowRight className="h-5 w-5 ml-2" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10" asChild>
                  <Link href="/en/contact">
                    Contact Us
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
