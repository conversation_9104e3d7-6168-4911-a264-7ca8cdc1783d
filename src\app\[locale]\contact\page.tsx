import { useTranslations } from 'next-intl';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { ContactForm } from '@/components/forms/contact-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  MessageCircle,
  Globe,
  Users,
  HeadphonesIcon
} from 'lucide-react';

const contactInfo = [
  {
    icon: MapPin,
    title: 'Main Office',
    details: [
      '45 SW Ankeny Street',
      'Portland, OR 97204',
      'United States'
    ],
    color: 'text-blue-600 dark:text-blue-400'
  },
  {
    icon: Phone,
    title: 'Phone',
    details: [
      '+****************',
      'Toll-free: 1-800-MERCY-01',
      'Emergency: +****************'
    ],
    color: 'text-green-600 dark:text-green-400'
  },
  {
    icon: Mail,
    title: 'Email',
    details: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ],
    color: 'text-purple-600 dark:text-purple-400'
  },
  {
    icon: Clock,
    title: 'Business Hours',
    details: [
      'Monday - Friday: 8:00 AM - 6:00 PM',
      'Saturday: 9:00 AM - 4:00 PM',
      'Sunday: Closed'
    ],
    color: 'text-orange-600 dark:text-orange-400'
  }
];

const supportChannels = [
  {
    icon: MessageCircle,
    title: 'Live Chat',
    description: 'Get instant help from our support team',
    availability: '24/7 Available',
    action: 'Start Chat'
  },
  {
    icon: HeadphonesIcon,
    title: 'Phone Support',
    description: 'Speak directly with our experts',
    availability: 'Mon-Fri 8AM-6PM',
    action: 'Call Now'
  },
  {
    icon: Mail,
    title: 'Email Support',
    description: 'Send us your questions and concerns',
    availability: 'Response within 24hrs',
    action: 'Send Email'
  },
  {
    icon: Globe,
    title: 'Help Center',
    description: 'Browse our comprehensive knowledge base',
    availability: 'Always Available',
    action: 'Visit Center'
  }
];

const offices = [
  {
    region: 'Middle East & North Africa',
    address: 'Amman, Jordan',
    phone: '+962 6 123 4567',
    email: '<EMAIL>'
  },
  {
    region: 'Sub-Saharan Africa',
    address: 'Nairobi, Kenya',
    phone: '+254 20 123 4567',
    email: '<EMAIL>'
  },
  {
    region: 'Asia Pacific',
    address: 'Manila, Philippines',
    phone: '+63 2 123 4567',
    email: '<EMAIL>'
  },
  {
    region: 'Latin America',
    address: 'Guatemala City, Guatemala',
    phone: '+502 2123 4567',
    email: '<EMAIL>'
  }
];

export default function ContactPage() {
  const t = useTranslations();

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-mercy-600 via-mercy-700 to-mercy-800 text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                {t('navigation.contact')}
              </h1>
              <p className="text-xl md:text-2xl text-mercy-100 leading-relaxed">
                We're here to help you on your journey to financial independence. 
                Reach out to us through any of the channels below.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Get in Touch
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Multiple ways to connect with our team and get the support you need.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {contactInfo.map((info, index) => {
                const Icon = info.icon;
                return (
                  <Card key={index} className="text-center hover:shadow-lg transition-all duration-300">
                    <CardHeader>
                      <div className="mx-auto mb-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-full w-fit">
                        <Icon className={`h-8 w-8 ${info.color}`} />
                      </div>
                      <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">
                        {info.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {info.details.map((detail, detailIndex) => (
                          <p key={detailIndex} className="text-gray-600 dark:text-gray-300 text-sm">
                            {detail}
                          </p>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Support Channels */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {supportChannels.map((channel, index) => {
                const Icon = channel.icon;
                return (
                  <Card key={index} className="hover:shadow-lg transition-all duration-300 cursor-pointer group">
                    <CardContent className="p-6 text-center">
                      <div className="mb-4 p-3 bg-mercy-100 dark:bg-mercy-900 rounded-full w-fit mx-auto group-hover:scale-110 transition-transform">
                        <Icon className="h-6 w-6 text-mercy-600 dark:text-mercy-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {channel.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                        {channel.description}
                      </p>
                      <div className="text-xs text-mercy-600 dark:text-mercy-400 font-medium mb-3">
                        {channel.availability}
                      </div>
                      <button className="text-mercy-600 dark:text-mercy-400 hover:text-mercy-700 dark:hover:text-mercy-300 font-medium text-sm transition-colors">
                        {channel.action} →
                      </button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Contact Form & Map */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                  Send us a Message
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mb-8">
                  Have a specific question or need personalized assistance? 
                  Fill out the form below and we'll get back to you within 24 hours.
                </p>
                <ContactForm />
              </div>

              {/* Map Placeholder */}
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                  Find Us
                </h2>
                <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-96 flex items-center justify-center mb-6">
                  <div className="text-center">
                    <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">
                      Interactive map would be integrated here
                    </p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
                      45 SW Ankeny Street, Portland, OR 97204
                    </p>
                  </div>
                </div>
                
                {/* Quick Info */}
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Visit Our Office
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <MapPin className="h-5 w-5 text-mercy-600 dark:text-mercy-400 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300 text-sm">
                          45 SW Ankeny Street, Portland, OR 97204
                        </span>
                      </div>
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <Clock className="h-5 w-5 text-mercy-600 dark:text-mercy-400 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300 text-sm">
                          Monday - Friday: 8:00 AM - 6:00 PM
                        </span>
                      </div>
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <Phone className="h-5 w-5 text-mercy-600 dark:text-mercy-400 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300 text-sm">
                          +****************
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Regional Offices */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Regional Offices
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Our global presence ensures we can provide localized support and services 
                to communities around the world.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {offices.map((office, index) => (
                <Card key={index} className="hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                      {office.region}
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <MapPin className="h-5 w-5 text-mercy-600 dark:text-mercy-400 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300">{office.address}</span>
                      </div>
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <Phone className="h-5 w-5 text-mercy-600 dark:text-mercy-400 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300">{office.phone}</span>
                      </div>
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <Mail className="h-5 w-5 text-mercy-600 dark:text-mercy-400 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300">{office.email}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Quick answers to common questions about our microfinance services.
              </p>
            </div>

            <div className="max-w-4xl mx-auto space-y-6">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    How long does the application process take?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Our streamlined application process typically takes 15 minutes to complete online. 
                    Once submitted, we review applications within 48-72 hours and provide a decision.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    What documents do I need to apply?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    You'll need a valid government-issued ID, business registration documents, 
                    bank statements from the last 3 months, a business plan, and proof of address.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    What are the interest rates?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Our interest rates range from 5-12% APR depending on the loan type, amount, 
                    and term. We offer competitive rates with no hidden fees.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Do you provide business training?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Yes! We offer comprehensive financial literacy and business management training 
                    programs to help ensure your business success.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
