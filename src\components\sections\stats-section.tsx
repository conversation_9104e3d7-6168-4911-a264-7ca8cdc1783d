'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { formatNumber } from '@/lib/utils';

const stats = [
  {
    key: 'loans_disbursed',
    value: 25000,
    suffix: '+',
    prefix: '$',
    multiplier: 1000000,
    description: 'In total funding provided'
  },
  {
    key: 'businesses_supported',
    value: 15000,
    suffix: '+',
    description: 'Small businesses empowered'
  },
  {
    key: 'jobs_created',
    value: 45000,
    suffix: '+',
    description: 'Employment opportunities created'
  },
  {
    key: 'communities_reached',
    value: 120,
    suffix: '+',
    description: 'Communities transformed'
  }
];

function CountUpAnimation({ 
  end, 
  duration = 2000, 
  prefix = '', 
  suffix = '', 
  multiplier = 1 
}: {
  end: number;
  duration?: number;
  prefix?: string;
  suffix?: string;
  multiplier?: number;
}) {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [end, duration]);

  const displayValue = multiplier > 1 ? count * multiplier : count;
  
  return (
    <span className="text-4xl md:text-5xl lg:text-6xl font-bold text-white">
      {prefix}{formatNumber(displayValue)}{suffix}
    </span>
  );
}

export function StatsSection() {
  const t = useTranslations();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    const element = document.getElementById('stats-section');
    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, []);

  return (
    <section 
      id="stats-section"
      className="py-20 bg-gradient-to-br from-mercy-600 via-mercy-700 to-mercy-800 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            {t('home.stats.title')}
          </h2>
          <p className="text-lg md:text-xl text-mercy-100 max-w-3xl mx-auto">
            Our impact speaks for itself. Here's how we're making a difference in communities worldwide.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div 
              key={stat.key}
              className="text-center group"
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 hover:bg-white/20 transition-all duration-300 group-hover:scale-105 border border-white/20">
                <div className="mb-4">
                  {isVisible ? (
                    <CountUpAnimation
                      end={stat.value}
                      prefix={stat.prefix}
                      suffix={stat.suffix}
                      multiplier={stat.multiplier}
                    />
                  ) : (
                    <span className="text-4xl md:text-5xl lg:text-6xl font-bold text-white">
                      {stat.prefix}0{stat.suffix}
                    </span>
                  )}
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {t(`home.stats.${stat.key}`)}
                </h3>
                <p className="text-mercy-100 text-sm">
                  {stat.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Impact Metrics */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="text-2xl font-bold text-white mb-2">98%</div>
            <div className="text-mercy-100">Loan Repayment Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white mb-2">72hrs</div>
            <div className="text-mercy-100">Average Processing Time</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white mb-2">4.9/5</div>
            <div className="text-mercy-100">Customer Satisfaction</div>
          </div>
        </div>
      </div>
    </section>
  );
}
