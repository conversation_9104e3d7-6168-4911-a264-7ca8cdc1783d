# Mercy Corps Microfinance Platform

A professional microfinance platform built for Mercy Corps, enabling communities to access financial services, apply for loans, and track their progress through an intuitive web interface.

## 🌟 Features

### Core Functionality
- **Multi-language Support**: Arabic and English with RTL support
- **Dark/Light Mode**: Complete theme switching capability
- **Responsive Design**: Mobile-first approach with professional UI
- **Application System**: Multi-step loan application process
- **User Dashboard**: Track applications and loan status
- **Admin Panel**: Complete management system for administrators

### Technical Features
- **Next.js 14**: Latest React framework with App Router
- **TypeScript**: Full type safety
- **Tailwind CSS**: Modern styling with custom design system
- **Supabase**: Backend as a service for database and authentication
- **Form Validation**: Robust form handling with Zod validation
- **Internationalization**: next-intl for seamless language switching
- **Professional Animations**: Smooth transitions and micro-interactions

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mercy-microfinance
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   Create a `.env.local` file in the root directory:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

   # App Configuration
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your_nextauth_secret
   ```

4. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
mercy-microfinance/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # Internationalized routes
│   │   │   ├── page.tsx       # Home page
│   │   │   ├── apply/         # Application pages
│   │   │   ├── dashboard/     # User dashboard
│   │   │   └── admin/         # Admin panel
│   │   └── globals.css        # Global styles
│   ├── components/            # Reusable components
│   │   ├── ui/               # Base UI components
│   │   ├── layout/           # Layout components
│   │   ├── sections/         # Page sections
│   │   └── forms/            # Form components
│   ├── lib/                  # Utility functions
│   └── i18n.ts              # Internationalization config
├── messages/                 # Translation files
│   ├── en.json              # English translations
│   └── ar.json              # Arabic translations
├── public/                  # Static assets
└── package.json            # Dependencies and scripts
```

## 🎨 Design System

### Colors
- **Primary**: Mercy Blue (#0ea5e9)
- **Secondary**: Supporting grays and whites
- **Accent**: Success, warning, and error states

### Typography
- **English**: Inter font family
- **Arabic**: Cairo font family
- **Responsive**: Fluid typography scaling

### Components
- **Cards**: Elevated surfaces with shadows
- **Buttons**: Multiple variants (primary, secondary, outline)
- **Forms**: Comprehensive form controls with validation
- **Navigation**: Responsive header with mobile menu

## 🌐 Internationalization

The platform supports:
- **English (en)**: Left-to-right layout
- **Arabic (ar)**: Right-to-left layout with Arabic fonts

Language switching is available in the header, and all content is fully translated.

## 📱 Responsive Design

- **Mobile First**: Optimized for mobile devices
- **Tablet**: Enhanced layouts for medium screens
- **Desktop**: Full-featured experience for large screens
- **Touch Friendly**: Appropriate touch targets and interactions

## 🔐 Authentication & Security

- **Supabase Auth**: Secure authentication system
- **Role-based Access**: Different access levels for users and admins
- **Form Validation**: Client and server-side validation
- **Data Protection**: Secure handling of sensitive information

## 📊 Features Overview

### For Applicants
- **Easy Application**: Multi-step application process
- **Document Upload**: Secure document submission
- **Status Tracking**: Real-time application status updates
- **Dashboard**: Personal dashboard with loan information

### For Administrators
- **Application Management**: Review and process applications
- **User Management**: Manage user accounts and permissions
- **Content Management**: Update website content and announcements
- **Analytics**: Track platform usage and loan statistics

## 🛠️ Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Quality
- **TypeScript**: Full type safety
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting (recommended)

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms
The application can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🙏 Acknowledgments

- **Mercy Corps**: For their mission and vision
- **Next.js Team**: For the amazing framework
- **Tailwind CSS**: For the utility-first CSS framework
- **Supabase**: For the backend infrastructure

---

Built with ❤️ for empowering communities through microfinance.
