'use client';

import { useTranslations, useLocale } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  DollarSign, 
  GraduationCap, 
  Users, 
  TrendingUp, 
  Shield, 
  Clock,
  Heart,
  Target
} from 'lucide-react';

const features = [
  {
    icon: DollarSign,
    titleKey: 'home.features.feature1.title',
    descriptionKey: 'home.features.feature1.description',
    color: 'text-green-600 dark:text-green-400'
  },
  {
    icon: GraduationCap,
    titleKey: 'home.features.feature2.title',
    descriptionKey: 'home.features.feature2.description',
    color: 'text-blue-600 dark:text-blue-400'
  },
  {
    icon: Users,
    titleKey: 'home.features.feature3.title',
    descriptionKey: 'home.features.feature3.description',
    color: 'text-purple-600 dark:text-purple-400'
  },
  {
    icon: TrendingUp,
    title: 'Growth Tracking',
    description: 'Monitor your business progress with our comprehensive tracking and reporting tools',
    color: 'text-orange-600 dark:text-orange-400'
  },
  {
    icon: Shield,
    title: 'Secure Platform',
    description: 'Your data and transactions are protected with bank-level security measures',
    color: 'text-red-600 dark:text-red-400'
  },
  {
    icon: Clock,
    title: 'Quick Processing',
    description: 'Fast application processing with decisions made within 48-72 hours',
    color: 'text-indigo-600 dark:text-indigo-400'
  }
];

export function FeaturesSection() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            {t('home.features.title')}
          </h2>
          <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {t('home.features.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card 
                key={index} 
                className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-0 shadow-lg bg-white dark:bg-gray-800"
              >
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto mb-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-full w-fit group-hover:scale-110 transition-transform duration-300">
                    <Icon className={`h-8 w-8 ${feature.color}`} />
                  </div>
                  <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">
                    {feature.titleKey ? t(feature.titleKey) : feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <CardDescription className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {feature.descriptionKey ? t(feature.descriptionKey) : feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Additional Features Grid */}
        <div className="mt-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="flex-shrink-0 p-3 bg-mercy-100 dark:bg-mercy-900 rounded-lg">
                  <Heart className="h-6 w-6 text-mercy-600 dark:text-mercy-400" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Community Impact
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Every loan creates a ripple effect of positive change in communities, 
                    supporting families and creating sustainable economic growth.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="flex-shrink-0 p-3 bg-mercy-100 dark:bg-mercy-900 rounded-lg">
                  <Target className="h-6 w-6 text-mercy-600 dark:text-mercy-400" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Targeted Solutions
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Customized financial products designed specifically for the unique 
                    needs of small businesses and entrepreneurs in developing communities.
                  </p>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="aspect-video rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                  alt="Community Impact"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -bottom-6 -right-6 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-xl border">
                <div className="text-center">
                  <div className="text-2xl font-bold text-mercy-600 dark:text-mercy-400">98%</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Success Rate</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
