'use client';

import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { 
  HandHeart, 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin,
  Youtube
} from 'lucide-react';

export function Footer() {
  const t = useTranslations();
  const locale = useLocale();

  const quickLinks = [
    { name: t('navigation.home'), href: `/${locale}` },
    { name: t('navigation.about'), href: `/${locale}/about` },
    { name: t('navigation.services'), href: `/${locale}/services` },
    { name: t('navigation.apply'), href: `/${locale}/apply` },
    { name: t('navigation.contact'), href: `/${locale}/contact` },
  ];

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: 'https://facebook.com/mercycorps' },
    { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/mercycorps' },
    { name: 'Instagram', icon: Instagram, href: 'https://instagram.com/mercycorps' },
    { name: 'LinkedIn', icon: Linkedin, href: 'https://linkedin.com/company/mercy-corps' },
    { name: 'YouTube', icon: Youtube, href: 'https://youtube.com/mercycorps' },
  ];

  return (
    <footer className="bg-gray-900 dark:bg-gray-950 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <Link href={`/${locale}`} className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="flex items-center justify-center w-10 h-10 bg-mercy-600 rounded-lg">
                <HandHeart className="h-6 w-6 text-white" />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-mercy-400">
                  Mercy Corps
                </span>
                <span className="text-xs text-gray-400">
                  Microfinance
                </span>
              </div>
            </Link>
            <p className="text-gray-300 text-sm leading-relaxed">
              {t('footer.description')}
            </p>
            <div className="flex space-x-4 rtl:space-x-reverse">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-mercy-400 transition-colors"
                  >
                    <Icon className="h-5 w-5" />
                    <span className="sr-only">{social.name}</span>
                  </a>
                );
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">
              {t('footer.quick_links')}
            </h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-mercy-400 transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">
              {t('footer.contact_info')}
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <MapPin className="h-5 w-5 text-mercy-400 mt-0.5 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p>45 SW Ankeny Street</p>
                  <p>Portland, OR 97204</p>
                  <p>United States</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Phone className="h-5 w-5 text-mercy-400 flex-shrink-0" />
                <span className="text-gray-300 text-sm">+****************</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Mail className="h-5 w-5 text-mercy-400 flex-shrink-0" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Newsletter Signup */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">
              Stay Updated
            </h3>
            <p className="text-gray-300 text-sm">
              Subscribe to our newsletter for the latest updates on our microfinance programs.
            </p>
            <div className="space-y-2">
              <input
                type="email"
                placeholder="Enter your email"
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-mercy-500 focus:border-transparent"
              />
              <button className="w-full bg-mercy-600 hover:bg-mercy-700 text-white px-4 py-2 rounded-md transition-colors text-sm font-medium">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-400 text-sm">
              {t('footer.copyright')}
            </p>
            <div className="flex space-x-6 rtl:space-x-reverse">
              <Link
                href={`/${locale}/privacy`}
                className="text-gray-400 hover:text-mercy-400 transition-colors text-sm"
              >
                Privacy Policy
              </Link>
              <Link
                href={`/${locale}/terms`}
                className="text-gray-400 hover:text-mercy-400 transition-colors text-sm"
              >
                Terms of Service
              </Link>
              <Link
                href={`/${locale}/cookies`}
                className="text-gray-400 hover:text-mercy-400 transition-colors text-sm"
              >
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
